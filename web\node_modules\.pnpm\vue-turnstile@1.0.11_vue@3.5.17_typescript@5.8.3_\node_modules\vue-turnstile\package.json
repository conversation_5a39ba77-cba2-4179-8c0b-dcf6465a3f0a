{"name": "vue-turnstile", "version": "1.0.11", "description": "Vue library for Cloudflare Turnstile CAPTCHA alternative", "type": "module", "files": ["dist"], "main": "./dist/vue-turnstile.umd.cjs", "module": "./dist/vue-turnstile.js", "types": "./dist/VueTurnstile.vue.d.ts", "exports": {".": {"types": "./dist/VueTurnstile.vue.d.ts", "import": "./dist/vue-turnstile.js", "require": "./dist/vue-turnstile.umd.cjs"}}, "scripts": {"dev": "vite", "build": "vite build && vue-tsc --emitDeclarationOnly", "preview": "vite preview"}, "homepage": "https://github.com/ruigomeseu/vue-turnstile", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://ruigomes.me"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ruigomeseu/vue-turnstile"}, "keywords": ["cloudflare", "turnstile", "vue", "<PERSON><PERSON>a", "vue3", "library"], "peerDependencies": {"vue": "^3.2.45"}, "devDependencies": {"@types/node": "^18.11.18", "@vitejs/plugin-vue": "^4.0.0", "prettier": "^2.8.1", "typescript": "^4.9.3", "vite": "^4.0.0", "vue": "^3.2.45", "vue-tsc": "^1.0.11"}}