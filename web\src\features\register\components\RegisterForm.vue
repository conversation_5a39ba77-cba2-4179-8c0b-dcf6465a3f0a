<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from '@/components/ui/form'
import { useToast } from '@/components/ui/toast'
import { registerSchema, useRegister } from '@/mutations/auth'
import { toTypedSchema } from '@vee-validate/zod'
import { ArrowRight, Loader, Eye, EyeOff } from 'lucide-vue-next'
import { effect, ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import TurnstileWidget from '@/components/ui/TurnstileWidget.vue'

const {toast} = useToast();

const typedRegisterSchema = toTypedSchema(registerSchema)

const registerMutation = useRegister();
const router = useRouter()

const showPassword = ref(false)
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const onSubmit = (values: any) => {
    registerMutation.mutate(values);
}

effect(function showToastOnSuccess() {
    if (registerMutation.data.value?.message) {
        toast({
            title: 'Registration successful',
            description: 'Your account has been created successfully, redirecting to login...',
            duration: 2000,
        })

        const timeout = setTimeout(() => {
            router.push('/login')
        }, 2000)

        return () => clearTimeout(timeout)
    }
})

</script>

<template>
  <div class="w-full h-full min-h-3xl flex bg-secondary p-6 rounded-4 max-w-7xl">
    <!-- Left side - Orange gradient with logo and back button -->
    <div class="flex-1 bg-gradient-to-br bg-primary p-8 flex flex-col relative rounded-2">
      <!-- Logo -->
      <div class="flex items-center mb-8">
        <div class="w-24 h-24 rounded-full flex items-center justify-center relative">
          <img src="/favicon.svg" alt="Logo" />
        </div>
      </div>

      <div class="absolute top-4 right-4">
        <Button
          variant="outline"
          class="bg-background/63 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm"
          as-child
        >
          <RouterLink to="/" class="!text-secondary flex items-center justify-center gap-2 !rounded-4">
            Back to website
            <ArrowRight class="w-12 h-12" />
          </RouterLink>
        </Button>
      </div>
    </div>

    <div class="flex-1 bg-secondary p-8 flex items-center justify-center">
      <div class="w-full max-w-md space-y-6">
        <!-- Welcome heading -->
        <div class="text-left space-y-2">
          <h1 class="text-white text-4xl font-medium">Create Account</h1>
          <p class="text-gray-300">
            Already have an account?
              <RouterLink
                to="/login"
                class="text-orange-400 hover:text-orange-300 underline-offset-4 hover:underline"
              >
                Sign in!
            </RouterLink>
          </p>
        </div>

        <Form @submit="onSubmit" :validation-schema="typedRegisterSchema" class="space-y-6">
          <FormField v-slot="{ componentField }" name="username">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Username</FormLabel>
              <FormControl>
                <input
                  v-bind="componentField"
                  type="text"
                  placeholder="Username"
                  autocomplete="username"
                  class="w-full px-3 py-2 bg-white rounded-md border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                />
              </FormControl>
              <p class="text-sm text-gray-400">Enter your desired username</p>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="email">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Email</FormLabel>
              <FormControl>
                <input
                  v-bind="componentField"
                  type="email"
                  placeholder="Email"
                  autocomplete="email"
                  class="w-full px-3 py-2 bg-white rounded-md border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                />
              </FormControl>
              <p class="text-sm text-gray-400">Enter your email address</p>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="password">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Password</FormLabel>
              <FormControl>
                <div class="relative">
                  <input
                    v-bind="componentField"
                    :type="showPassword ? 'text' : 'password'"
                    placeholder="Password"
                    autocomplete="new-password"
                    class="w-full px-3 py-2 pr-10 bg-white rounded-md border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                  />
                  <button
                    type="button"
                    @click="togglePasswordVisibility"
                    class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
                  >
                    <Eye v-if="!showPassword" class="w-5 h-5" />
                    <EyeOff v-else class="w-5 h-5" />
                  </button>
                </div>
              </FormControl>
              <p class="text-sm text-gray-400">Enter your password</p>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="gender">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Gender</FormLabel>
              <FormControl>
                <select
                  v-bind="componentField"
                  class="w-full px-3 py-2 bg-white rounded-md border border-gray-300 text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-colors"
                >
                  <option value="">Select gender</option>
                  <option value="M">Male</option>
                  <option value="F">Female</option>
                </select>
              </FormControl>
              <p class="text-sm text-gray-400">Select your gender</p>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="terms">
            <FormItem>
              <FormControl>
                <div class="flex items-center space-x-2">
                  <input
                    v-bind="componentField"
                    type="checkbox"
                    id="terms"
                    class="w-4 h-4 text-orange-400 bg-white border-gray-300 rounded focus:ring-orange-400 focus:ring-2"
                  />
                  <label for="terms" class="text-white text-sm">
                    I accept the
                    <a href="#" class="text-orange-400 hover:text-orange-300 underline-offset-4 hover:underline">
                      terms and conditions
                    </a>
                  </label>
                </div>
              </FormControl>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="captcha">
            <FormItem>
              <FormLabel class="text-white text-sm font-medium">Verification</FormLabel>
              <FormControl>
                <TurnstileWidget
                  v-bind="componentField"
                  @error="() => componentField.onChange('')"
                  @expired="() => componentField.onChange('')"
                  theme="light"
                  size="normal"
                />
              </FormControl>
              <FormMessage class="text-red-400 text-sm" />
            </FormItem>
          </FormField>

          <div v-if="registerMutation.data.value?.error" class="text-red-500">
            {{ registerMutation.data.value?.error ||"" }}
          </div>

          <Button
            type="submit"
            size="lg"
            variant="default"
            class="w-full !bg-primary !hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md transition-colors"
            :disabled="registerMutation.isPending.value"
          >
            Create Account
            <Loader v-if="registerMutation.isPending.value" class="w-12 h-12 animate-spin" />
          </Button>
        </Form>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>