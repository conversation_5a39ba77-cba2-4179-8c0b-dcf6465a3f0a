import { defineComponent as o, openBlock as d, createElementBlock as u } from "vue";
const c = "https://challenges.cloudflare.com/turnstile/v0/api.js", l = "cfTurnstileOnLoad";
let i = typeof window < "u" && window.turnstile !== void 0 ? "ready" : "unloaded", n;
const p = o({
  name: "VueTurnstile",
  emits: ["update:modelValue", "error", "unsupported", "expired", "before-interactive", "after-interactive"],
  props: {
    siteKey: {
      type: String,
      required: !0
    },
    modelValue: {
      type: String,
      required: !0
    },
    resetInterval: {
      type: Number,
      required: !1,
      default: 295 * 1e3
    },
    size: {
      type: String,
      required: !1,
      default: "normal"
    },
    theme: {
      type: String,
      required: !1,
      default: "auto"
    },
    language: {
      type: String,
      required: !1,
      default: "auto"
    },
    action: {
      type: String,
      required: !1,
      default: ""
    },
    appearance: {
      type: String,
      required: !1,
      default: "always"
    },
    renderOnMount: {
      type: Boolean,
      required: !1,
      default: !0
    }
  },
  data() {
    return {
      resetTimeout: void 0,
      widgetId: void 0
    };
  },
  computed: {
    turnstileOptions() {
      return {
        sitekey: this.siteKey,
        theme: this.theme,
        language: this.language,
        size: this.size,
        callback: this.callback,
        action: this.action,
        appearance: this.appearance,
        "error-callback": this.errorCallback,
        "expired-callback": this.expiredCallback,
        "unsupported-callback": this.unsupportedCallback,
        "before-interactive-callback": this.beforeInteractiveCallback,
        "after-interactive-callback": this.afterInteractivecallback
      };
    }
  },
  methods: {
    afterInteractivecallback() {
      this.$emit("after-interactive");
    },
    beforeInteractiveCallback() {
      this.$emit("before-interactive");
    },
    expiredCallback() {
      this.$emit("expired");
    },
    unsupportedCallback() {
      this.$emit("unsupported");
    },
    errorCallback(e) {
      this.$emit("error", e);
    },
    callback(e) {
      this.$emit("update:modelValue", e), this.startResetTimeout();
    },
    reset() {
      window.turnstile && (this.$emit("update:modelValue", ""), window.turnstile.reset());
    },
    remove() {
      this.widgetId && (window.turnstile.remove(this.widgetId), this.widgetId = void 0);
    },
    render() {
      this.widgetId = window.turnstile.render(this.$refs.turnstile, this.turnstileOptions);
    },
    startResetTimeout() {
      this.resetTimeout = setTimeout(() => {
        this.reset();
      }, this.resetInterval);
    }
  },
  async mounted() {
    const e = new Promise((r, t) => {
      n = { resolve: r, reject: t }, i === "ready" && r(void 0);
    });
    window[l] = () => {
      n.resolve(), i = "ready";
    }, await (() => {
      if (i === "unloaded") {
        i = "loading";
        const r = `${c}?onload=${l}&render=explicit`, t = document.createElement("script");
        t.src = r, t.async = !0, t.addEventListener("error", () => {
          n.reject("Failed to load Turnstile.");
        }), document.head.appendChild(t);
      }
      return e;
    })(), this.renderOnMount && this.render();
  },
  beforeUnmount() {
    this.remove(), clearTimeout(this.resetTimeout);
  }
}), f = (e, a) => {
  const r = e.__vccOpts || e;
  for (const [t, s] of a)
    r[t] = s;
  return r;
}, h = { ref: "turnstile" };
function m(e, a, r, t, s, b) {
  return d(), u("div", h, null, 512);
}
const k = /* @__PURE__ */ f(p, [["render", m]]);
export {
  k as default
};
