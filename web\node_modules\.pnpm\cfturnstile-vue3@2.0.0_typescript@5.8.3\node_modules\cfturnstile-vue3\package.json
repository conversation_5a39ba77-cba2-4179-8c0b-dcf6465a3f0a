{"name": "cfturnstile-vue3", "version": "2.0.0", "main": "./dist/cfturnstile-vue3.umd.js", "scripts": {"build": "vue-tsc --declaration --emitDeclarationOnly && vite build", "prepare": "npm run build"}, "typings": "./dist_types/index.d.ts", "sideEffects": false, "dependencies": {"vue": "^3.2.38"}, "devDependencies": {"@types/node": "^16.11.56", "@vitejs/plugin-vue": "^3.0.3", "@vue/tsconfig": "^0.1.3", "npm-run-all": "^4.1.5", "typescript": "~4.7.4", "vite": "^3.0.9", "vue-tsc": "^0.40.13"}, "exports": {".": {"import": "./dist/cfturnstile-vue3.mjs", "require": "./dist/cfturnstile-vue3.umd.js", "types": "./dist_types/index.d.ts"}}, "homepage": "https://github.com/Astrian/cfturnstile-vue3", "license": "MIT", "bugs": {"url": "https://github.com/Astrian/cfturnstile-vue3/issues"}}