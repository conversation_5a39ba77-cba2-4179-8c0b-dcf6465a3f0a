<template>
  <div :id="containerId" class="turnstile-widget"></div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { TURNSTILE_SITE_KEY } from '@/lib/config'

interface Props {
  theme?: 'light' | 'dark' | 'auto'
  size?: 'normal' | 'compact' | 'flexible'
  modelValue?: string
  // Additional props that might come from vee-validate componentField
  onChange?: (e: Event | unknown) => void
  onBlur?: (e: Event) => void
  onInput?: (e: Event | unknown) => void
  name?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'error'): void
  (e: 'expired'): void
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'light',
  size: 'normal',
  modelValue: ''
})

const emit = defineEmits<Emits>()

// Generate unique container ID for each widget instance
const containerId = `turnstile-${Math.random().toString(36).substring(2, 11)}`
const widgetId = ref<string | null>(null)

const loadTurnstileScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if Turnstile is already loaded
    if (window.turnstile) {
      resolve()
      return
    }

    // Check if script is already being loaded
    const existingScript = document.querySelector('script[src*="turnstile"]')
    if (existingScript) {
      existingScript.addEventListener('load', () => resolve())
      existingScript.addEventListener('error', reject)
      return
    }

    // Load the script
    const script = document.createElement('script')
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js'
    script.async = true
    script.defer = true
    script.onload = () => resolve()
    script.onerror = reject
    document.head.appendChild(script)
  })
}

const renderWidget = async () => {
  try {
    await loadTurnstileScript()
    
    // Wait a bit for the script to initialize
    await new Promise(resolve => setTimeout(resolve, 100))
    
    if (window.turnstile && document.getElementById(containerId)) {
      widgetId.value = window.turnstile.render(`#${containerId}`, {
        sitekey: TURNSTILE_SITE_KEY,
        callback: (token: string) => {
          emit('update:modelValue', token)
          // Also call the vee-validate onChange if provided
          if (props.onChange) {
            props.onChange(token)
          }
        },
        'error-callback': () => {
          emit('update:modelValue', '')
          emit('error')
          if (props.onChange) {
            props.onChange('')
          }
        },
        'expired-callback': () => {
          emit('update:modelValue', '')
          emit('expired')
          if (props.onChange) {
            props.onChange('')
          }
        },
        theme: props.theme,
        size: props.size,
      })
    }
  } catch (error) {
    console.error('Failed to load Turnstile:', error)
    emit('error')
  }
}

const resetWidget = () => {
  if (window.turnstile && widgetId.value) {
    window.turnstile.reset(widgetId.value)
    emit('update:modelValue', '')
  }
}

const removeWidget = () => {
  if (window.turnstile && widgetId.value) {
    window.turnstile.remove(widgetId.value)
    widgetId.value = null
  }
}

// Watch for external value changes to reset if needed
watch(() => props.modelValue, (newValue) => {
  if (!newValue && widgetId.value) {
    resetWidget()
  }
})

onMounted(() => {
  renderWidget()
})

onUnmounted(() => {
  removeWidget()
})

// Expose reset method for parent components
defineExpose({
  reset: resetWidget
})

// Declare global Turnstile types
declare global {
  interface Window {
    turnstile?: {
      render: (container: string, options: any) => string
      reset: (widgetId?: string) => void
      remove: (widgetId?: string) => void
      getResponse: (widgetId?: string) => string
    }
  }
}
</script>

<style scoped>
.turnstile-widget {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
