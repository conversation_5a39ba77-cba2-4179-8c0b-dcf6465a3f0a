#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/Dev/reforged/web/node_modules/.pnpm/vitest@3.2.4_@types+node@22_ab96c7e4964e5ffac452301536ee951c/node_modules/vitest/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/vitest@3.2.4_@types+node@22_ab96c7e4964e5ffac452301536ee951c/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/Dev/reforged/web/node_modules/.pnpm/vitest@3.2.4_@types+node@22_ab96c7e4964e5ffac452301536ee951c/node_modules/vitest/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/vitest@3.2.4_@types+node@22_ab96c7e4964e5ffac452301536ee951c/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/vitest@3.2.4_@types+node@22_ab96c7e4964e5ffac452301536ee951c/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../.pnpm/vitest@3.2.4_@types+node@22_ab96c7e4964e5ffac452301536ee951c/node_modules/vitest/vitest.mjs" "$@"
fi
