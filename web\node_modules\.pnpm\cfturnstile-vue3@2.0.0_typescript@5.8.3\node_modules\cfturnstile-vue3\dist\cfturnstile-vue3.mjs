import { defineComponent as d, onMounted as s, openBlock as o, createElementBlock as f } from "vue";
const b = d({
  name: "TurnstileComponent",
  props: {
    sitekey: {
      type: String,
      required: !0
    },
    action: {
      type: String,
      required: !1
    },
    cData: {
      type: String,
      required: !1
    },
    execution: {
      type: String,
      required: !1
    },
    theme: {
      type: String,
      required: !1
    },
    language: {
      type: String,
      required: !1
    },
    tabindex: {
      type: Number,
      required: !1
    },
    responseField: {
      type: String,
      required: !1
    },
    responseFieldName: {
      type: String,
      required: !1
    },
    size: {
      type: String,
      required: !1
    },
    retry: {
      type: String,
      required: !1
    },
    retryInterval: {
      type: Number,
      required: !1
    },
    refreshExpired: {
      type: String
    },
    refreshTimeout: {
      type: String,
      required: !1
    },
    appearance: {
      type: String,
      required: !1
    },
    feedbackEnabled: {
      type: Boolean,
      required: !1
    }
  },
  emits: {
    callback: (e) => e !== null && e !== "",
    errorCallback: (e) => e !== null && e !== "",
    expiredCallback: null,
    beforeInteractiveCallback: null,
    afterInteractiveCallback: null,
    unsupportedCallback: null,
    timeoutCallback: null
  },
  setup(e, r) {
    s(() => {
      if (window.turnstile === null || !window.turnstile) {
        const t = document.createElement("script");
        t.src = "https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback", t.async = !0, t.defer = !0, document.head.appendChild(t);
      }
      a();
    });
    function a() {
      window.onloadTurnstileCallback = () => {
        var t, l, n, u, c;
        (c = window.turnstile) == null || c.render("#turnstile-box", {
          sitekey: e.sitekey,
          callback: (i) => r.emit("callback", i),
          "expired-callback": () => r.emit("expiredCallback"),
          "error-callback": (i) => r.emit("errorCallback", i),
          action: e.action,
          cData: e.cData,
          execution: (t = e.execution) != null ? t : "render",
          "before-interactive-callback": () => r.emit("beforeInteractiveCallback"),
          "after-interactive-callback": () => r.emit("afterInteractiveCallback"),
          "unsupported-callback": () => r.emit("unsupportedCallback"),
          theme: (l = e.theme) != null ? l : "auto",
          language: (n = e.language) != null ? n : "auto",
          tabindex: (u = e.tabindex) != null ? u : 0,
          "timeout-callback": () => r.emit("timeoutCallback"),
          "response-field": e.responseField,
          "response-field-name": e.responseFieldName,
          size: e.size,
          retry: e.retry,
          "retry-interval": e.retryInterval,
          "refresh-expired": e.refreshExpired,
          "refresh-timeout": e.refreshTimeout,
          apperance: e.appearance,
          "feedback-enabled": e.feedbackEnabled
        });
      };
    }
  }
}), m = (e, r) => {
  const a = e.__vccOpts || e;
  for (const [t, l] of r)
    a[t] = l;
  return a;
}, k = {
  ref: "turnstileBox",
  id: "turnstile-box"
};
function p(e, r, a, t, l, n) {
  return o(), f("div", k, null, 512);
}
const g = /* @__PURE__ */ m(b, [["render", p]]);
export {
  g as default
};
