(function(n,a){typeof exports=="object"&&typeof module<"u"?module.exports=a(require("vue")):typeof define=="function"&&define.amd?define(["vue"],a):(n=typeof globalThis<"u"?globalThis:n||self,n.TurnstileComponent=a(n.Vue))})(this,function(n){"use strict";const a=n.defineComponent({name:"TurnstileComponent",props:{sitekey:{type:String,required:!0},action:{type:String,required:!1},cData:{type:String,required:!1},execution:{type:String,required:!1},theme:{type:String,required:!1},language:{type:String,required:!1},tabindex:{type:Number,required:!1},responseField:{type:String,required:!1},responseFieldName:{type:String,required:!1},size:{type:String,required:!1},retry:{type:String,required:!1},retryInterval:{type:Number,required:!1},refreshExpired:{type:String},refreshTimeout:{type:String,required:!1},appearance:{type:String,required:!1},feedbackEnabled:{type:Boolean,required:!1}},emits:{callback:e=>e!==null&&e!=="",errorCallback:e=>e!==null&&e!=="",expiredCallback:null,beforeInteractiveCallback:null,afterInteractiveCallback:null,unsupportedCallback:null,timeoutCallback:null},setup(e,t){n.onMounted(()=>{if(window.turnstile===null||!window.turnstile){const r=document.createElement("script");r.src="https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback",r.async=!0,r.defer=!0,document.head.appendChild(r)}l()});function l(){window.onloadTurnstileCallback=()=>{var r,i,u,d,s;(s=window.turnstile)==null||s.render("#turnstile-box",{sitekey:e.sitekey,callback:c=>t.emit("callback",c),"expired-callback":()=>t.emit("expiredCallback"),"error-callback":c=>t.emit("errorCallback",c),action:e.action,cData:e.cData,execution:(r=e.execution)!=null?r:"render","before-interactive-callback":()=>t.emit("beforeInteractiveCallback"),"after-interactive-callback":()=>t.emit("afterInteractiveCallback"),"unsupported-callback":()=>t.emit("unsupportedCallback"),theme:(i=e.theme)!=null?i:"auto",language:(u=e.language)!=null?u:"auto",tabindex:(d=e.tabindex)!=null?d:0,"timeout-callback":()=>t.emit("timeoutCallback"),"response-field":e.responseField,"response-field-name":e.responseFieldName,size:e.size,retry:e.retry,"retry-interval":e.retryInterval,"refresh-expired":e.refreshExpired,"refresh-timeout":e.refreshTimeout,apperance:e.appearance,"feedback-enabled":e.feedbackEnabled})}}}}),o=(e,t)=>{const l=e.__vccOpts||e;for(const[r,i]of t)l[r]=i;return l},f={ref:"turnstileBox",id:"turnstile-box"};function b(e,t,l,r,i,u){return n.openBlock(),n.createElementBlock("div",f,null,512)}return o(a,[["render",b]])});
