import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    sitekey: {
        type: StringConstructor;
        required: true;
    };
    action: {
        type: StringConstructor;
        required: false;
    };
    cData: {
        type: StringConstructor;
        required: false;
    };
    execution: {
        type: PropType<"render" | "execute">;
        required: false;
    };
    theme: {
        type: PropType<"light" | "dark" | "auto">;
        required: false;
    };
    language: {
        type: StringConstructor;
        required: false;
    };
    tabindex: {
        type: NumberConstructor;
        required: false;
    };
    responseField: {
        type: StringConstructor;
        required: false;
    };
    responseFieldName: {
        type: StringConstructor;
        required: false;
    };
    size: {
        type: PropType<"normal" | "flexible" | "compact">;
        required: false;
    };
    retry: {
        type: PropType<"auto" | "never">;
        required: false;
    };
    retryInterval: {
        type: NumberConstructor;
        required: false;
    };
    refreshExpired: {
        type: PropType<"auto" | "never" | "manual">;
    };
    refreshTimeout: {
        type: PropType<"auto" | "never" | "manual">;
        required: false;
    };
    appearance: {
        type: PropType<"execute" | "always" | "interaction-only">;
        required: false;
    };
    feedbackEnabled: {
        type: BooleanConstructor;
        required: false;
    };
}, void, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    callback: (response: string) => boolean;
    errorCallback: (response: string) => boolean;
    expiredCallback: null;
    beforeInteractiveCallback: null;
    afterInteractiveCallback: null;
    unsupportedCallback: null;
    timeoutCallback: null;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    sitekey: {
        type: StringConstructor;
        required: true;
    };
    action: {
        type: StringConstructor;
        required: false;
    };
    cData: {
        type: StringConstructor;
        required: false;
    };
    execution: {
        type: PropType<"render" | "execute">;
        required: false;
    };
    theme: {
        type: PropType<"light" | "dark" | "auto">;
        required: false;
    };
    language: {
        type: StringConstructor;
        required: false;
    };
    tabindex: {
        type: NumberConstructor;
        required: false;
    };
    responseField: {
        type: StringConstructor;
        required: false;
    };
    responseFieldName: {
        type: StringConstructor;
        required: false;
    };
    size: {
        type: PropType<"normal" | "flexible" | "compact">;
        required: false;
    };
    retry: {
        type: PropType<"auto" | "never">;
        required: false;
    };
    retryInterval: {
        type: NumberConstructor;
        required: false;
    };
    refreshExpired: {
        type: PropType<"auto" | "never" | "manual">;
    };
    refreshTimeout: {
        type: PropType<"auto" | "never" | "manual">;
        required: false;
    };
    appearance: {
        type: PropType<"execute" | "always" | "interaction-only">;
        required: false;
    };
    feedbackEnabled: {
        type: BooleanConstructor;
        required: false;
    };
}>> & {
    onExpiredCallback?: ((...args: any[]) => any) | undefined;
    onBeforeInteractiveCallback?: ((...args: any[]) => any) | undefined;
    onAfterInteractiveCallback?: ((...args: any[]) => any) | undefined;
    onUnsupportedCallback?: ((...args: any[]) => any) | undefined;
    onTimeoutCallback?: ((...args: any[]) => any) | undefined;
    onCallback?: ((response: string) => any) | undefined;
    onErrorCallback?: ((response: string) => any) | undefined;
}, {
    feedbackEnabled: boolean;
}>;
export default _default;
