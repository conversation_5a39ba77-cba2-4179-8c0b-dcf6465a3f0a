# Cloudflare Turnstile Integration

This project uses Cloudflare Turnstile as a CAPTCHA alternative for the login and register forms.

## Setup Instructions

1. **Get your Turnstile Site Key:**
   - Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - Navigate to "Turnstile" in the sidebar
   - Create a new site or use an existing one
   - Copy your site key

2. **Configure Environment Variables:**
   - Copy `.env.example` to `.env`
   - Set your Turnstile site key:
     ```
     VITE_TURNSTILE_SITE_KEY=your_actual_site_key_here
     ```

3. **Backend Integration:**
   - Your backend needs to verify the Turnstile token
   - Use the secret key (different from site key) to verify tokens
   - See [Cloudflare Turnstile Server-side Validation](https://developers.cloudflare.com/turnstile/get-started/server-side-validation/)

## Implementation Details

- **Package Used:** `cfturnstile-vue3` v2.0.0
- **Forms Integrated:** Login and Register forms
- **Component:** Custom `TurnstileWidget.vue` wrapper for better lifecycle management
- **Validation:** Integrated with vee-validate and Zod schemas

## Features

- ✅ Automatic script loading
- ✅ Unique widget IDs to prevent conflicts
- ✅ Proper cleanup on component unmount
- ✅ Error and expiration handling
- ✅ Integration with existing form validation
- ✅ Responsive design

## Testing

For testing purposes, you can use Cloudflare's test site keys:
- **Always passes:** `1x00000000000000000000AA`
- **Always fails:** `2x00000000000000000000AB`
- **Always blocks:** `3x00000000000000000000FF`

## Troubleshooting

If the Turnstile widget disappears when navigating between pages:
- This is handled by the custom `TurnstileWidget.vue` component
- Each widget gets a unique container ID
- Proper cleanup prevents conflicts

If you see "Invalid site key" errors:
- Check that `VITE_TURNSTILE_SITE_KEY` is set correctly
- Ensure the domain matches what's configured in Cloudflare
- For localhost development, make sure to add `localhost` to allowed domains
