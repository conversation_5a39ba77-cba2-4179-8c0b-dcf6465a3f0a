(function(r,n){typeof exports=="object"&&typeof module<"u"?module.exports=n(require("vue")):typeof define=="function"&&define.amd?define(["vue"],n):(r=typeof globalThis<"u"?globalThis:r||self,r.VueTurnstile=n(r.Vue))})(this,function(r){"use strict";const n="https://challenges.cloudflare.com/turnstile/v0/api.js",o="cfTurnstileOnLoad";let a=typeof window<"u"&&window.turnstile!==void 0?"ready":"unloaded",s;const d=r.defineComponent({name:"VueTurnstile",emits:["update:modelValue","error","unsupported","expired","before-interactive","after-interactive"],props:{siteKey:{type:String,required:!0},modelValue:{type:String,required:!0},resetInterval:{type:Number,required:!1,default:295*1e3},size:{type:String,required:!1,default:"normal"},theme:{type:String,required:!1,default:"auto"},language:{type:String,required:!1,default:"auto"},action:{type:String,required:!1,default:""},appearance:{type:String,required:!1,default:"always"},renderOnMount:{type:Boolean,required:!1,default:!0}},data(){return{resetTimeout:void 0,widgetId:void 0}},computed:{turnstileOptions(){return{sitekey:this.siteKey,theme:this.theme,language:this.language,size:this.size,callback:this.callback,action:this.action,appearance:this.appearance,"error-callback":this.errorCallback,"expired-callback":this.expiredCallback,"unsupported-callback":this.unsupportedCallback,"before-interactive-callback":this.beforeInteractiveCallback,"after-interactive-callback":this.afterInteractivecallback}}},methods:{afterInteractivecallback(){this.$emit("after-interactive")},beforeInteractiveCallback(){this.$emit("before-interactive")},expiredCallback(){this.$emit("expired")},unsupportedCallback(){this.$emit("unsupported")},errorCallback(e){this.$emit("error",e)},callback(e){this.$emit("update:modelValue",e),this.startResetTimeout()},reset(){window.turnstile&&(this.$emit("update:modelValue",""),window.turnstile.reset())},remove(){this.widgetId&&(window.turnstile.remove(this.widgetId),this.widgetId=void 0)},render(){this.widgetId=window.turnstile.render(this.$refs.turnstile,this.turnstileOptions)},startResetTimeout(){this.resetTimeout=setTimeout(()=>{this.reset()},this.resetInterval)}},async mounted(){const e=new Promise((i,t)=>{s={resolve:i,reject:t},a==="ready"&&i(void 0)});window[o]=()=>{s.resolve(),a="ready"},await(()=>{if(a==="unloaded"){a="loading";const i=`${n}?onload=${o}&render=explicit`,t=document.createElement("script");t.src=i,t.async=!0,t.addEventListener("error",()=>{s.reject("Failed to load Turnstile.")}),document.head.appendChild(t)}return e})(),this.renderOnMount&&this.render()},beforeUnmount(){this.remove(),clearTimeout(this.resetTimeout)}}),c=(e,l)=>{const i=e.__vccOpts||e;for(const[t,u]of l)i[t]=u;return i},p={ref:"turnstile"};function f(e,l,i,t,u,m){return r.openBlock(),r.createElementBlock("div",p,null,512)}return c(d,[["render",f]])});
