import type { PropType } from 'vue';
declare const turnstileLoadFunction = "cfTurnstileOnLoad";
declare global {
    interface Window {
        turnstile: any;
        [turnstileLoadFunction]: any;
    }
}
declare interface VueTurnstileData {
    resetTimeout?: ReturnType<typeof setTimeout>;
    widgetId?: string;
}
declare const _default: import("vue").DefineComponent<{
    siteKey: {
        type: StringConstructor;
        required: true;
    };
    modelValue: {
        type: StringConstructor;
        required: true;
    };
    resetInterval: {
        type: NumberConstructor;
        required: false;
        default: number;
    };
    size: {
        type: PropType<"normal" | "flexible" | "compact">;
        required: false;
        default: string;
    };
    theme: {
        type: PropType<"light" | "dark" | "auto">;
        required: false;
        default: string;
    };
    language: {
        type: StringConstructor;
        required: false;
        default: string;
    };
    action: {
        type: StringConstructor;
        required: false;
        default: string;
    };
    appearance: {
        type: PropType<"always" | "execute" | "interaction-only">;
        required: false;
        default: string;
    };
    renderOnMount: {
        type: BooleanConstructor;
        required: false;
        default: boolean;
    };
}, unknown, VueTurnstileData, {
    turnstileOptions(): {
        sitekey: string;
        theme: "light" | "dark" | "auto";
        language: string;
        size: "normal" | "flexible" | "compact";
        callback: (token: string) => void;
        action: string;
        appearance: "always" | "execute" | "interaction-only";
        'error-callback': (code: string) => void;
        'expired-callback': () => void;
        'unsupported-callback': () => void;
        'before-interactive-callback': () => void;
        'after-interactive-callback': () => void;
    };
}, {
    afterInteractivecallback(): void;
    beforeInteractiveCallback(): void;
    expiredCallback(): void;
    unsupportedCallback(): void;
    errorCallback(code: string): void;
    callback(token: string): void;
    reset(): void;
    remove(): void;
    render(): void;
    startResetTimeout(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("update:modelValue" | "error" | "unsupported" | "expired" | "before-interactive" | "after-interactive")[], "update:modelValue" | "error" | "unsupported" | "expired" | "before-interactive" | "after-interactive", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    siteKey: {
        type: StringConstructor;
        required: true;
    };
    modelValue: {
        type: StringConstructor;
        required: true;
    };
    resetInterval: {
        type: NumberConstructor;
        required: false;
        default: number;
    };
    size: {
        type: PropType<"normal" | "flexible" | "compact">;
        required: false;
        default: string;
    };
    theme: {
        type: PropType<"light" | "dark" | "auto">;
        required: false;
        default: string;
    };
    language: {
        type: StringConstructor;
        required: false;
        default: string;
    };
    action: {
        type: StringConstructor;
        required: false;
        default: string;
    };
    appearance: {
        type: PropType<"always" | "execute" | "interaction-only">;
        required: false;
        default: string;
    };
    renderOnMount: {
        type: BooleanConstructor;
        required: false;
        default: boolean;
    };
}>> & {
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
    onError?: ((...args: any[]) => any) | undefined;
    onUnsupported?: ((...args: any[]) => any) | undefined;
    onExpired?: ((...args: any[]) => any) | undefined;
    "onBefore-interactive"?: ((...args: any[]) => any) | undefined;
    "onAfter-interactive"?: ((...args: any[]) => any) | undefined;
}, {
    resetInterval: number;
    size: "normal" | "flexible" | "compact";
    theme: "light" | "dark" | "auto";
    language: string;
    action: string;
    appearance: "always" | "execute" | "interaction-only";
    renderOnMount: boolean;
}>;
export default _default;
