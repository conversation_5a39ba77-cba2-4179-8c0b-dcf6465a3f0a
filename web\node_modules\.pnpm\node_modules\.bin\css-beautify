#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/bin/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/bin/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/js-beautify@1.15.4/node_modules:/mnt/f/Dev/reforged/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../js-beautify/js/bin/css-beautify.js" "$@"
else
  exec node  "$basedir/../js-beautify/js/bin/css-beautify.js" "$@"
fi
